import os
import warnings

import urllib3
from dotenv import load_dotenv
from haystack import Document
from haystack.components.embedders import OpenAIDocumentEmbedder, OpenAITextEmbedder
from haystack.document_stores.types import DuplicatePolicy
from haystack.utils import Secret
from haystack_integrations.components.retrievers.opensearch import (
    OpenSearchHybridRetriever,
)
from haystack_integrations.document_stores.opensearch import OpenSearchDocumentStore

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings("ignore", message=".*verify_certs=False.*")

load_dotenv(override=True)


class RetrievalCore:
    def __init__(self):
        self.doc_store = OpenSearchDocumentStore(
            hosts=f"https://{os.environ['OPENSEARCH_HOST']}",
            http_auth=(
                os.environ["OPENSEARCH_USERNAME"],
                os.environ["OPENSEARCH_PASSWORD"],
            ),
            index=os.environ["OPENSEARCH_INDEX"],
            embedding_dim=os.environ["EMBEDDING_DIMS"],
            verify_certs=False,
            use_ssl=True,
        )

        self.embed_config = {
            "api_base_url": os.environ["EMBEDDING_BASE_URL"],
            "api_key": Secret.from_token(os.environ["EMBEDDING_API_KEY"]),
            "model": os.environ["EMBEDDING_MODEL"],
            "http_client_kwargs": {"verify": False, "timeout": 30.0},
        }

        self.doc_embedder = OpenAIDocumentEmbedder(**self.embed_config)
        self.text_embedder = OpenAITextEmbedder(**self.embed_config)

        self.retriever = OpenSearchHybridRetriever(
            document_store=self.doc_store,
            embedder=self.text_embedder,
            top_k_bm25=5,  # Increased from 3 to capture more potential matches
            top_k_embedding=5,  # Increased from 3 to capture more potential matches
            join_mode="reciprocal_rank_fusion",
        )

    def insert_documents(self, documents: list[dict], content_key: str = "msg"):
        docs_list = [
            Document(
                content=d[content_key],
                meta={
                    # Rename 'id' to 'message_id' to avoid conflict with OpenSearch internal ID
                    ("message_id" if k == "id" else k): v
                    for k, v in d.items()
                    if k != content_key
                },
            )
            for d in documents
        ]
        docs = self.doc_embedder.run(documents=docs_list)
        self.doc_store.write_documents(docs["documents"], policy=DuplicatePolicy.SKIP)

    def search(self, query: str, filters: dict = None, top_k: int = None):
        """Search documents with query and metadata filters.

        Args:
            query: Search query string
            filters: Dictionary of metadata filters, e.g. {"caseId": "123"}
            top_k: Number of documents to return (overrides default if provided)
        """
        # Use dynamic top_k if provided, otherwise use default retriever settings
        if top_k:
            # Create a new retriever instance with custom top_k for this query
            # Create a new text embedder instance to avoid pipeline component sharing
            custom_text_embedder = OpenAITextEmbedder(**self.embed_config)
            custom_retriever = OpenSearchHybridRetriever(
                document_store=self.doc_store,
                embedder=custom_text_embedder,
                top_k_bm25=top_k,
                top_k_embedding=top_k,
                join_mode="reciprocal_rank_fusion",
            )
            results = custom_retriever.run(
                query=query, filters_bm25=filters, filters_embedding=filters
            )
        else:
            results = self.retriever.run(
                query=query, filters_bm25=filters, filters_embedding=filters
            )

        documents = results["documents"]

        # Sort documents by message_id in meta data
        documents.sort(key=lambda doc: doc.meta.get("message_id", 0))

        # Apply score threshold filtering to improve precision
        if documents:
            # Calculate dynamic threshold based on top score
            top_score = max(doc.score for doc in documents)
            score_threshold = (
                top_score * 0.6
            )  # Keep docs with score >= 60% of top score
            documents = [doc for doc in documents if doc.score >= score_threshold]

        return documents

    def delete_all_documents(self):
        documents = self.doc_store.filter_documents()
        # print(documents)
        for doc in documents:
            self.doc_store.delete_documents([doc.id])


if __name__ == "__main__":
    docs = [
        {"msg": "Machine learning is a subset of artificial intelligence.", "id": 1},
        {"msg": "Deep learning is a subset of machine learning.", "id": 2},
        {"msg": "Natural language processing is a field of AI.", "id": 3},
        {"msg": "Reinforcement learning is a type of machine learning.", "id": 4},
        {"msg": "Supervised learning is a type of machine learning.", "id": 5},
    ]

    knowledge_base = RetrievalCore()
    knowledge_base.insert_documents(docs)
    print(knowledge_base.search("What is reinforcement learning?"))

    print(knowledge_base.doc_store.count_documents())
    knowledge_base.delete_all_documents()
    print(knowledge_base.doc_store.count_documents())
